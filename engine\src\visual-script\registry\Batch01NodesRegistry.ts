/**
 * 批次0.1渲染系统节点注册表
 * 统一管理批次0.1的74个渲染系统节点注册
 * 包括材质管理、后处理效果、着色器、渲染优化等节点
 */
import { NodeRegistry } from './NodeRegistry';
import { NodeCategory } from '../types/NodeTypes';

// 导入材质管理节点
import {
  MaterialSystemNode,
  CreateMaterialNode,
  SetMaterialPropertyNode,
  GetMaterialPropertyNode,
  MaterialBlendNode,
  MaterialAnimationNode,
  MaterialOptimizationNode,
  PBRMaterialNode,
  StandardMaterialNode,
  CustomMaterialNode,
  MaterialPresetNode
} from '../nodes/material/MaterialNodes';

import {
  MaterialEditorNode,
  MaterialPreviewNode,
  MaterialLibraryNode
} from '../nodes/material/MaterialEditingNodes';

import {
  MaterialImportNode,
  MaterialExportNode,
  MaterialValidationNode,
  MaterialVersioningNode,
  MaterialSharingNode,
  MaterialAnalyticsNode
} from '../nodes/material/MaterialEditingNodes2';

// 扩展材质节点
import {
  MaterialNodeEditorNode,
  MaterialShaderEditorNode,
  MaterialTextureEditorNode,
  MaterialParameterEditorNode
} from '../nodes/material/MaterialEditingNodes3';

// 导入后处理效果节点
import {
  BloomEffectNode,
  BlurEffectNode,
  ColorGradingNode,
  ToneMappingNode,
  SSAONode,
  SSRNode
} from '../nodes/rendering/PostProcessingEffectNodes';

import {
  MotionBlurNode,
  DepthOfFieldNode,
  FilmGrainNode,
  VignetteNode,
  ChromaticAberrationNode,
  LensDistortionNode,
  AntiAliasingNode,
  HDRProcessingNode,
  CustomPostProcessNode
} from '../nodes/rendering/AdvancedPostProcessingNodes';

// 导入着色器节点
import {
  VertexShaderNode,
  FragmentShaderNode,
  ComputeShaderNode,
  ShaderCompilerNode,
  ShaderOptimizationNode
} from '../nodes/rendering/ShaderNodes';

import {
  ShaderVariantNode,
  ShaderParameterNode,
  ShaderIncludeNode,
  ShaderMacroNode
} from '../nodes/rendering/AdvancedShaderNodes';

import {
  ShaderDebugNode,
  ShaderPerformanceAnalysisNode,
  ShaderValidationNode,
  ShaderCacheNode,
  ShaderHotReloadNode,
  ShaderExportNode
} from '../nodes/rendering/ShaderUtilityNodes';

// 导入渲染优化节点
import {
  LODSystemNode,
  FrustumCullingNode,
  OcclusionCullingNode,
  BatchRenderingNode,
  InstancedRenderingNode,
  DrawCallOptimizationNode,
  TextureAtlasNode,
  MeshCombiningNode,
  RenderQueueNode,
  PerformanceProfilerNode,
  RenderStatisticsNode,
  GPUMemoryMonitorNode,
  RenderPipelineNode,
  CustomRenderPassNode,
  RenderTargetNode
} from '../nodes/rendering/RenderingOptimizationNodes';

/**
 * 批次0.1节点注册表类
 */
export class Batch01NodesRegistry {
  private static instance: Batch01NodesRegistry;
  private nodeRegistry: NodeRegistry;
  private registeredNodes: Map<string, any> = new Map();

  private constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  public static getInstance(): Batch01NodesRegistry {
    if (!Batch01NodesRegistry.instance) {
      Batch01NodesRegistry.instance = new Batch01NodesRegistry();
    }
    return Batch01NodesRegistry.instance;
  }

  /**
   * 注册所有批次0.1节点
   */
  public registerAllNodes(): void {
    this.registerMaterialNodes();
    this.registerPostProcessingNodes();
    this.registerShaderNodes();
    this.registerRenderingOptimizationNodes();
    
    console.log('批次0.1渲染系统节点注册完成');
    console.log(`材质管理节点：24个`);
    console.log(`后处理效果节点：15个`);
    console.log(`着色器节点：15个`);
    console.log(`渲染优化节点：15个`);
    console.log(`总计：69个节点`);
  }

  /**
   * 注册材质管理节点 (24个)
   */
  private registerMaterialNodes(): void {
    // 核心材质节点 (11个)
    this.registerNode(MaterialSystemNode, 'Material/Core', '#FF9800');
    this.registerNode(CreateMaterialNode, 'Material/Core', '#FF9800');
    this.registerNode(SetMaterialPropertyNode, 'Material/Core', '#FF9800');
    this.registerNode(GetMaterialPropertyNode, 'Material/Core', '#FF9800');
    this.registerNode(MaterialBlendNode, 'Material/Core', '#FF9800');
    this.registerNode(MaterialAnimationNode, 'Material/Core', '#FF9800');
    this.registerNode(MaterialOptimizationNode, 'Material/Core', '#FF9800');
    this.registerNode(PBRMaterialNode, 'Material/Core', '#FF9800');
    this.registerNode(StandardMaterialNode, 'Material/Core', '#FF9800');
    this.registerNode(CustomMaterialNode, 'Material/Core', '#FF9800');
    this.registerNode(MaterialPresetNode, 'Material/Core', '#FF9800');

    // 材质编辑节点 (9个)
    this.registerNode(MaterialEditorNode, 'Material/Editor', '#FFC107');
    this.registerNode(MaterialPreviewNode, 'Material/Editor', '#FFC107');
    this.registerNode(MaterialLibraryNode, 'Material/Editor', '#FFC107');
    this.registerNode(MaterialImportNode, 'Material/Editor', '#FFC107');
    this.registerNode(MaterialExportNode, 'Material/Editor', '#FFC107');
    this.registerNode(MaterialValidationNode, 'Material/Editor', '#FFC107');
    this.registerNode(MaterialVersioningNode, 'Material/Editor', '#FFC107');
    this.registerNode(MaterialSharingNode, 'Material/Editor', '#FFC107');
    this.registerNode(MaterialAnalyticsNode, 'Material/Editor', '#FFC107');

    // 扩展材质节点 (4个)
    this.registerNode(MaterialNodeEditorNode, 'Material/Advanced', '#FFEB3B');
    this.registerNode(MaterialShaderEditorNode, 'Material/Advanced', '#FFEB3B');
    this.registerNode(MaterialTextureEditorNode, 'Material/Advanced', '#FFEB3B');
    this.registerNode(MaterialParameterEditorNode, 'Material/Advanced', '#FFEB3B');
  }

  /**
   * 注册后处理效果节点 (15个)
   */
  private registerPostProcessingNodes(): void {
    // 基础后处理节点 (6个)
    this.registerNode(BloomEffectNode, 'Rendering/PostProcess', '#E91E63');
    this.registerNode(BlurEffectNode, 'Rendering/PostProcess', '#E91E63');
    this.registerNode(ColorGradingNode, 'Rendering/PostProcess', '#E91E63');
    this.registerNode(ToneMappingNode, 'Rendering/PostProcess', '#E91E63');
    this.registerNode(SSAONode, 'Rendering/PostProcess', '#E91E63');
    this.registerNode(SSRNode, 'Rendering/PostProcess', '#E91E63');

    // 高级后处理节点 (9个)
    this.registerNode(MotionBlurNode, 'Rendering/PostProcess', '#E91E63');
    this.registerNode(DepthOfFieldNode, 'Rendering/PostProcess', '#E91E63');
    this.registerNode(FilmGrainNode, 'Rendering/PostProcess', '#E91E63');
    this.registerNode(VignetteNode, 'Rendering/PostProcess', '#E91E63');
    this.registerNode(ChromaticAberrationNode, 'Rendering/PostProcess', '#E91E63');
    this.registerNode(LensDistortionNode, 'Rendering/PostProcess', '#E91E63');
    this.registerNode(AntiAliasingNode, 'Rendering/PostProcess', '#E91E63');
    this.registerNode(HDRProcessingNode, 'Rendering/PostProcess', '#E91E63');
    this.registerNode(CustomPostProcessNode, 'Rendering/PostProcess', '#E91E63');
  }

  /**
   * 注册着色器节点 (15个)
   */
  private registerShaderNodes(): void {
    // 核心着色器节点 (5个)
    this.registerNode(VertexShaderNode, 'Shader/Core', '#9C27B0');
    this.registerNode(FragmentShaderNode, 'Shader/Core', '#9C27B0');
    this.registerNode(ComputeShaderNode, 'Shader/Core', '#9C27B0');
    this.registerNode(ShaderCompilerNode, 'Shader/Tools', '#9C27B0');
    this.registerNode(ShaderOptimizationNode, 'Shader/Tools', '#9C27B0');

    // 高级着色器节点 (4个)
    this.registerNode(ShaderVariantNode, 'Shader/Advanced', '#9C27B0');
    this.registerNode(ShaderParameterNode, 'Shader/Advanced', '#9C27B0');
    this.registerNode(ShaderIncludeNode, 'Shader/Advanced', '#9C27B0');
    this.registerNode(ShaderMacroNode, 'Shader/Advanced', '#9C27B0');

    // 着色器工具节点 (6个)
    this.registerNode(ShaderDebugNode, 'Shader/Debug', '#9C27B0');
    this.registerNode(ShaderPerformanceAnalysisNode, 'Shader/Debug', '#9C27B0');
    this.registerNode(ShaderValidationNode, 'Shader/Debug', '#9C27B0');
    this.registerNode(ShaderCacheNode, 'Shader/Utility', '#9C27B0');
    this.registerNode(ShaderHotReloadNode, 'Shader/Utility', '#9C27B0');
    this.registerNode(ShaderExportNode, 'Shader/Utility', '#9C27B0');
  }

  /**
   * 注册渲染优化节点 (15个)
   */
  private registerRenderingOptimizationNodes(): void {
    // 核心优化节点 (5个)
    this.registerNode(LODSystemNode, 'Rendering/Optimization', '#4CAF50');
    this.registerNode(FrustumCullingNode, 'Rendering/Optimization', '#4CAF50');
    this.registerNode(OcclusionCullingNode, 'Rendering/Optimization', '#4CAF50');
    this.registerNode(BatchRenderingNode, 'Rendering/Optimization', '#4CAF50');
    this.registerNode(InstancedRenderingNode, 'Rendering/Optimization', '#4CAF50');

    // 性能优化节点 (5个)
    this.registerNode(DrawCallOptimizationNode, 'Rendering/Optimization', '#4CAF50');
    this.registerNode(TextureAtlasNode, 'Rendering/Optimization', '#4CAF50');
    this.registerNode(MeshCombiningNode, 'Rendering/Optimization', '#4CAF50');
    this.registerNode(RenderQueueNode, 'Rendering/Optimization', '#4CAF50');
    this.registerNode(PerformanceProfilerNode, 'Rendering/Analysis', '#4CAF50');

    // 监控和管线节点 (5个)
    this.registerNode(RenderStatisticsNode, 'Rendering/Analysis', '#4CAF50');
    this.registerNode(GPUMemoryMonitorNode, 'Rendering/Analysis', '#4CAF50');
    this.registerNode(RenderPipelineNode, 'Rendering/Pipeline', '#4CAF50');
    this.registerNode(CustomRenderPassNode, 'Rendering/Pipeline', '#4CAF50');
    this.registerNode(RenderTargetNode, 'Rendering/Pipeline', '#4CAF50');
  }

  /**
   * 通用节点注册方法
   */
  private registerNode(nodeClass: any, category: string, color: string): void {
    const nodeType = nodeClass.TYPE;
    const nodeName = nodeClass.NAME;
    const nodeDescription = nodeClass.DESCRIPTION;

    if (this.registeredNodes.has(nodeType)) {
      console.warn(`节点 ${nodeType} 已经注册，跳过重复注册`);
      return;
    }

    this.nodeRegistry.registerNode({
      type: nodeType,
      name: nodeName,
      description: nodeDescription,
      category: category,
      nodeClass: nodeClass,
      icon: this.getNodeIcon(nodeType),
      color: color,
      tags: this.getNodeTags(nodeType)
    });

    this.registeredNodes.set(nodeType, nodeClass);
  }

  /**
   * 获取节点图标
   */
  private getNodeIcon(nodeType: string): string {
    // 材质节点图标
    if (nodeType.includes('Material')) {
      return 'palette';
    }
    // 着色器节点图标
    if (nodeType.includes('Shader')) {
      return 'code';
    }
    // 后处理效果节点图标
    if (nodeType.includes('Effect') || nodeType.includes('Process')) {
      return 'filter';
    }
    // 渲染优化节点图标
    if (nodeType.includes('Optimization') || nodeType.includes('LOD') || nodeType.includes('Culling')) {
      return 'speed';
    }
    // 默认图标
    return 'extension';
  }

  /**
   * 获取节点标签
   */
  private getNodeTags(nodeType: string): string[] {
    const tags: string[] = ['batch01', 'rendering'];
    
    if (nodeType.includes('Material')) {
      tags.push('material', 'graphics');
    }
    if (nodeType.includes('Shader')) {
      tags.push('shader', 'glsl');
    }
    if (nodeType.includes('Effect') || nodeType.includes('Process')) {
      tags.push('postprocess', 'effect');
    }
    if (nodeType.includes('Optimization')) {
      tags.push('optimization', 'performance');
    }
    
    return tags;
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return Array.from(this.registeredNodes.keys());
  }

  /**
   * 获取批次0.1统计信息
   */
  public getBatch01Statistics(): any {
    return {
      totalNodes: 69,
      categories: {
        materialManagement: 24,
        postProcessing: 15,
        shader: 15,
        renderingOptimization: 15
      },
      features: [
        '材质系统管理',
        '材质编辑器',
        '材质库管理',
        '后处理效果链',
        '着色器编译器',
        '着色器调试器',
        'LOD系统',
        '视锥体剔除',
        '批处理渲染',
        '性能分析器'
      ],
      compatibility: {
        editor: true,
        runtime: true,
        webgl: true,
        mobile: true
      }
    };
  }

  /**
   * 验证批次0.1节点完整性
   */
  public validateBatch01Nodes(): boolean {
    const expectedNodes = 69;
    const registeredNodes = this.registeredNodes.size;
    
    if (registeredNodes !== expectedNodes) {
      console.error(`批次0.1节点注册不完整：期望 ${expectedNodes} 个，实际 ${registeredNodes} 个`);
      return false;
    }
    
    console.log('批次0.1节点验证通过：所有69个节点已正确注册');
    return true;
  }
}

/**
 * 导出批次0.1节点注册函数
 */
export function registerBatch01Nodes(): void {
  const registry = Batch01NodesRegistry.getInstance();
  registry.registerAllNodes();
}

/**
 * 导出批次0.1节点验证函数
 */
export function validateBatch01Nodes(): boolean {
  const registry = Batch01NodesRegistry.getInstance();
  return registry.validateBatch01Nodes();
}
