/**
 * 批次0.1节点编辑器集成测试
 * 测试节点在编辑器中的集成和界面功能
 */
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { Batch01NodesIntegration, integrateBatch01Nodes } from '../Batch01NodesIntegration';

/**
 * 模拟节点编辑器
 */
class MockNodeEditor {
  private palette: Map<string, any> = new Map();
  private categories: Map<string, any> = new Map();

  addNodeToPalette(nodeType: string, nodeConfig: any): void {
    this.palette.set(nodeType, nodeConfig);
  }

  addNodeCategory(category: string, categoryInfo: any): void {
    this.categories.set(category, categoryInfo);
  }

  getPaletteNodes(): Map<string, any> {
    return this.palette;
  }

  getCategories(): Map<string, any> {
    return this.categories;
  }

  clearPalette(): void {
    this.palette.clear();
  }

  clearCategories(): void {
    this.categories.clear();
  }
}

describe('批次0.1节点编辑器集成测试', () => {
  let mockNodeEditor: MockNodeEditor;
  let integration: Batch01NodesIntegration;

  beforeEach(() => {
    jest.clearAllMocks();
    mockNodeEditor = new MockNodeEditor();
    integration = new Batch01NodesIntegration(mockNodeEditor as any);
  });

  afterEach(() => {
    jest.restoreAllMocks();
    mockNodeEditor.clearPalette();
    mockNodeEditor.clearCategories();
  });

  describe('集成初始化测试', () => {
    test('应该正确创建集成实例', () => {
      expect(integration).toBeInstanceOf(Batch01NodesIntegration);
      expect(integration.getRegisteredNodes()).toBeInstanceOf(Map);
      expect(integration.getNodeCategories()).toBeInstanceOf(Map);
    });

    test('应该正确初始化节点分类', () => {
      const categories = integration.getNodeCategories();
      
      // 检查是否初始化了所有11个分类
      expect(categories.size).toBe(11);
      expect(categories.has('Material/Core')).toBe(true);
      expect(categories.has('Material/Editor')).toBe(true);
      expect(categories.has('Material/Advanced')).toBe(true);
      expect(categories.has('Rendering/PostProcess')).toBe(true);
      expect(categories.has('Shader/Core')).toBe(true);
      expect(categories.has('Shader/Advanced')).toBe(true);
      expect(categories.has('Shader/Debug')).toBe(true);
      expect(categories.has('Shader/Utility')).toBe(true);
      expect(categories.has('Rendering/Optimization')).toBe(true);
      expect(categories.has('Rendering/Analysis')).toBe(true);
      expect(categories.has('Rendering/Pipeline')).toBe(true);
    });
  });

  describe('节点集成测试', () => {
    beforeEach(() => {
      integration.integrateAllNodes();
    });

    test('应该注册所有69个节点', () => {
      const registeredNodes = integration.getRegisteredNodes();
      expect(registeredNodes.size).toBe(69);
    });

    test('应该正确注册材质管理节点', () => {
      const registeredNodes = integration.getRegisteredNodes();
      
      // 核心材质节点
      expect(registeredNodes.has('MaterialSystem')).toBe(true);
      expect(registeredNodes.has('CreateMaterial')).toBe(true);
      expect(registeredNodes.has('SetMaterialProperty')).toBe(true);
      expect(registeredNodes.has('PBRMaterial')).toBe(true);
      
      // 材质编辑节点
      expect(registeredNodes.has('MaterialEditor')).toBe(true);
      expect(registeredNodes.has('MaterialLibrary')).toBe(true);
      
      // 高级材质节点
      expect(registeredNodes.has('MaterialNodeEditor')).toBe(true);
      expect(registeredNodes.has('MaterialShaderEditor')).toBe(true);
    });

    test('应该正确注册后处理效果节点', () => {
      const registeredNodes = integration.getRegisteredNodes();
      
      expect(registeredNodes.has('BloomEffect')).toBe(true);
      expect(registeredNodes.has('BlurEffect')).toBe(true);
      expect(registeredNodes.has('ColorGrading')).toBe(true);
      expect(registeredNodes.has('SSAO')).toBe(true);
      expect(registeredNodes.has('MotionBlur')).toBe(true);
      expect(registeredNodes.has('DepthOfField')).toBe(true);
      expect(registeredNodes.has('HDRProcessing')).toBe(true);
    });

    test('应该正确注册着色器节点', () => {
      const registeredNodes = integration.getRegisteredNodes();
      
      // 核心着色器节点
      expect(registeredNodes.has('VertexShader')).toBe(true);
      expect(registeredNodes.has('FragmentShader')).toBe(true);
      expect(registeredNodes.has('ComputeShader')).toBe(true);
      
      // 高级着色器节点
      expect(registeredNodes.has('ShaderVariant')).toBe(true);
      expect(registeredNodes.has('ShaderParameter')).toBe(true);
      
      // 着色器工具节点
      expect(registeredNodes.has('ShaderDebug')).toBe(true);
      expect(registeredNodes.has('ShaderCache')).toBe(true);
    });

    test('应该正确注册渲染优化节点', () => {
      const registeredNodes = integration.getRegisteredNodes();
      
      // 核心优化节点
      expect(registeredNodes.has('LODSystem')).toBe(true);
      expect(registeredNodes.has('FrustumCulling')).toBe(true);
      expect(registeredNodes.has('BatchRendering')).toBe(true);
      
      // 性能优化节点
      expect(registeredNodes.has('DrawCallOptimization')).toBe(true);
      expect(registeredNodes.has('TextureAtlas')).toBe(true);
      
      // 监控和管线节点
      expect(registeredNodes.has('RenderStatistics')).toBe(true);
      expect(registeredNodes.has('RenderPipeline')).toBe(true);
    });

    test('应该正确设置节点配置', () => {
      const registeredNodes = integration.getRegisteredNodes();
      const materialSystemConfig = registeredNodes.get('MaterialSystem');
      
      expect(materialSystemConfig).toBeDefined();
      expect(materialSystemConfig.type).toBe('MaterialSystem');
      expect(materialSystemConfig.name).toBe('材质系统');
      expect(materialSystemConfig.category).toBe('Material/Core');
      expect(materialSystemConfig.icon).toBe('palette');
      expect(materialSystemConfig.color).toBe('#FF9800');
      expect(materialSystemConfig.tags).toContain('material');
      expect(materialSystemConfig.tags).toContain('core');
      expect(materialSystemConfig.tags).toContain('batch01');
    });
  });

  describe('节点面板集成测试', () => {
    beforeEach(() => {
      integration.integrateAllNodes();
    });

    test('应该将所有节点添加到编辑器面板', () => {
      const paletteNodes = mockNodeEditor.getPaletteNodes();
      expect(paletteNodes.size).toBe(69);
    });

    test('应该正确设置节点分类', () => {
      const categories = mockNodeEditor.getCategories();
      expect(categories.size).toBe(11);
      
      const materialCoreCategory = categories.get('Material/Core');
      expect(materialCoreCategory).toBeDefined();
      expect(materialCoreCategory.displayName).toBe('材质核心');
      expect(materialCoreCategory.icon).toBe('palette');
      expect(materialCoreCategory.color).toBe('#FF9800');
    });

    test('应该正确分配节点到分类', () => {
      const nodeCategories = integration.getNodeCategories();
      
      const materialCoreNodes = nodeCategories.get('Material/Core');
      expect(materialCoreNodes).toBeDefined();
      expect(materialCoreNodes.length).toBe(11);
      
      const shaderCoreNodes = nodeCategories.get('Shader/Core');
      expect(shaderCoreNodes).toBeDefined();
      expect(shaderCoreNodes.length).toBe(5);
      
      const postProcessNodes = nodeCategories.get('Rendering/PostProcess');
      expect(postProcessNodes).toBeDefined();
      expect(postProcessNodes.length).toBe(15);
    });
  });

  describe('节点查询和验证测试', () => {
    beforeEach(() => {
      integration.integrateAllNodes();
    });

    test('应该正确检查节点注册状态', () => {
      expect(integration.isNodeRegistered('MaterialSystem')).toBe(true);
      expect(integration.isNodeRegistered('BloomEffect')).toBe(true);
      expect(integration.isNodeRegistered('VertexShader')).toBe(true);
      expect(integration.isNodeRegistered('LODSystem')).toBe(true);
      expect(integration.isNodeRegistered('NonExistentNode')).toBe(false);
    });

    test('应该正确获取节点配置', () => {
      const bloomConfig = integration.getNodeConfig('BloomEffect');
      expect(bloomConfig).toBeDefined();
      expect(bloomConfig.type).toBe('BloomEffect');
      expect(bloomConfig.category).toBe('Rendering/PostProcess');
      
      const nonExistentConfig = integration.getNodeConfig('NonExistentNode');
      expect(nonExistentConfig).toBeUndefined();
    });

    test('应该正确获取所有注册的节点类型', () => {
      const registeredTypes = Array.from(integration.getRegisteredNodes().keys());
      expect(registeredTypes).toHaveLength(69);
      expect(registeredTypes).toContain('MaterialSystem');
      expect(registeredTypes).toContain('BloomEffect');
      expect(registeredTypes).toContain('VertexShader');
      expect(registeredTypes).toContain('LODSystem');
    });
  });

  describe('节点显示名称和描述测试', () => {
    test('应该正确生成节点显示名称', () => {
      const integration = new Batch01NodesIntegration(mockNodeEditor as any);
      
      // 测试私有方法通过反射访问
      const getNodeDisplayName = (integration as any).getNodeDisplayName;
      
      expect(getNodeDisplayName('MaterialSystem')).toBe('材质系统');
      expect(getNodeDisplayName('CreateMaterial')).toBe('创建材质');
      expect(getNodeDisplayName('BloomEffect')).toBe('泛光效果');
      expect(getNodeDisplayName('VertexShader')).toBe('顶点着色器');
      expect(getNodeDisplayName('LODSystem')).toBe('LOD系统');
      
      // 测试未映射的节点名称
      expect(getNodeDisplayName('UnknownNode')).toBe('UnknownNode');
    });

    test('应该正确生成节点描述', () => {
      const integration = new Batch01NodesIntegration(mockNodeEditor as any);
      
      // 测试私有方法通过反射访问
      const getNodeDescription = (integration as any).getNodeDescription;
      
      expect(getNodeDescription('MaterialSystem')).toBe('批次0.1渲染系统节点 - 材质系统');
      expect(getNodeDescription('BloomEffect')).toBe('批次0.1渲染系统节点 - 泛光效果');
    });
  });

  describe('集成函数测试', () => {
    test('integrateBatch01Nodes函数应该正确工作', () => {
      const result = integrateBatch01Nodes(mockNodeEditor as any);
      
      expect(result).toBeInstanceOf(Batch01NodesIntegration);
      expect(result.getRegisteredNodes().size).toBe(69);
      expect(result.getNodeCategories().size).toBe(11);
      
      // 验证节点已添加到编辑器
      const paletteNodes = mockNodeEditor.getPaletteNodes();
      expect(paletteNodes.size).toBe(69);
      
      // 验证分类已添加到编辑器
      const categories = mockNodeEditor.getCategories();
      expect(categories.size).toBe(11);
    });
  });

  describe('错误处理测试', () => {
    test('应该处理空的节点编辑器', () => {
      expect(() => {
        new Batch01NodesIntegration(null as any);
      }).not.toThrow();
    });

    test('应该处理节点注册失败', () => {
      const faultyEditor = {
        addNodeToPalette: jest.fn().mockImplementation(() => {
          throw new Error('注册失败');
        }),
        addNodeCategory: jest.fn()
      };

      expect(() => {
        const integration = new Batch01NodesIntegration(faultyEditor as any);
        integration.integrateAllNodes();
      }).not.toThrow(); // 应该优雅地处理错误
    });

    test('应该处理分类设置失败', () => {
      const faultyEditor = {
        addNodeToPalette: jest.fn(),
        addNodeCategory: jest.fn().mockImplementation(() => {
          throw new Error('分类设置失败');
        })
      };

      expect(() => {
        const integration = new Batch01NodesIntegration(faultyEditor as any);
        integration.integrateAllNodes();
      }).not.toThrow(); // 应该优雅地处理错误
    });
  });

  describe('性能测试', () => {
    test('节点集成应该在合理时间内完成', () => {
      const startTime = performance.now();
      
      integration.integrateAllNodes();
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 集成应该在100ms内完成
      expect(duration).toBeLessThan(100);
    });

    test('重复集成应该不会造成内存泄漏', () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      // 执行多次集成
      for (let i = 0; i < 10; i++) {
        const newIntegration = new Batch01NodesIntegration(mockNodeEditor as any);
        newIntegration.integrateAllNodes();
      }
      
      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      // 内存增长应该在合理范围内（如果支持内存监控）
      if (initialMemory > 0 && finalMemory > 0) {
        const memoryIncrease = finalMemory - initialMemory;
        expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // 10MB
      }
    });
  });

  describe('兼容性测试', () => {
    test('应该与不同版本的节点编辑器兼容', () => {
      // 模拟旧版本编辑器（缺少某些方法）
      const oldEditor = {
        addNodeToPalette: jest.fn()
        // 缺少 addNodeCategory 方法
      };

      expect(() => {
        const integration = new Batch01NodesIntegration(oldEditor as any);
        integration.integrateAllNodes();
      }).not.toThrow();
    });

    test('应该处理编辑器方法返回值的变化', () => {
      const editorWithReturnValues = {
        addNodeToPalette: jest.fn().mockReturnValue(true),
        addNodeCategory: jest.fn().mockReturnValue({ success: true })
      };

      expect(() => {
        const integration = new Batch01NodesIntegration(editorWithReturnValues as any);
        integration.integrateAllNodes();
      }).not.toThrow();
    });
  });
});
