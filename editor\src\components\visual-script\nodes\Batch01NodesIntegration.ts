/**
 * 批次0.1渲染系统节点编辑器集成
 * 负责将批次0.1的69个渲染系统节点集成到编辑器界面中
 */
import { NodeEditor } from '../NodeEditor';
import { NodeCategory } from '../../../types/NodeTypes';

/**
 * 批次0.1节点配置接口
 */
interface Batch01NodeConfig {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  color: string;
  tags: string[];
  nodeClass: any;
}

/**
 * 批次0.1节点分类映射
 */
const BATCH01_CATEGORY_MAP = {
  'Material/Core': {
    displayName: '材质核心',
    icon: 'palette',
    color: '#FF9800',
    description: '核心材质管理功能'
  },
  'Material/Editor': {
    displayName: '材质编辑',
    icon: 'edit',
    color: '#FFC107',
    description: '材质编辑和管理工具'
  },
  'Material/Advanced': {
    displayName: '高级材质',
    icon: 'experiment',
    color: '#FFEB3B',
    description: '高级材质编辑功能'
  },
  'Rendering/PostProcess': {
    displayName: '后处理效果',
    icon: 'filter',
    color: '#E91E63',
    description: '后处理效果和滤镜'
  },
  'Shader/Core': {
    displayName: '核心着色器',
    icon: 'code',
    color: '#9C27B0',
    description: '核心着色器功能'
  },
  'Shader/Advanced': {
    displayName: '高级着色器',
    icon: 'function',
    color: '#9C27B0',
    description: '高级着色器功能'
  },
  'Shader/Debug': {
    displayName: '着色器调试',
    icon: 'bug',
    color: '#F44336',
    description: '着色器调试工具'
  },
  'Shader/Utility': {
    displayName: '着色器工具',
    icon: 'tool',
    color: '#009688',
    description: '着色器实用工具'
  },
  'Rendering/Optimization': {
    displayName: '渲染优化',
    icon: 'speed',
    color: '#4CAF50',
    description: '渲染性能优化'
  },
  'Rendering/Analysis': {
    displayName: '渲染分析',
    icon: 'analytics',
    color: '#4CAF50',
    description: '渲染性能分析'
  },
  'Rendering/Pipeline': {
    displayName: '渲染管线',
    icon: 'linear_scale',
    color: '#4CAF50',
    description: '渲染管线管理'
  }
};

/**
 * 批次0.1节点编辑器集成类
 */
export class Batch01NodesIntegration {
  private nodeEditor: NodeEditor;
  private registeredNodes: Map<string, Batch01NodeConfig> = new Map();
  private categoryNodes: Map<string, Batch01NodeConfig[]> = new Map();

  constructor(nodeEditor: NodeEditor) {
    this.nodeEditor = nodeEditor;
    this.initializeCategories();
  }

  /**
   * 初始化节点分类
   */
  private initializeCategories(): void {
    for (const category of Object.keys(BATCH01_CATEGORY_MAP)) {
      this.categoryNodes.set(category, []);
    }
  }

  /**
   * 集成所有批次0.1节点
   */
  public integrateAllNodes(): void {
    this.integrateMaterialNodes();
    this.integratePostProcessingNodes();
    this.integrateShaderNodes();
    this.integrateRenderingOptimizationNodes();
    this.setupNodePalette();
    this.setupNodeCategories();
    
    console.log('批次0.1节点编辑器集成完成');
    console.log(`总计集成节点：${this.registeredNodes.size}个`);
    console.log(`节点分类：${this.categoryNodes.size}个`);
  }

  /**
   * 集成材质管理节点 (24个)
   */
  private integrateMaterialNodes(): void {
    // 核心材质节点 (11个)
    const coreNodes = [
      'MaterialSystem', 'CreateMaterial', 'SetMaterialProperty', 'GetMaterialProperty',
      'MaterialBlend', 'MaterialAnimation', 'MaterialOptimization', 'PBRMaterial',
      'StandardMaterial', 'CustomMaterial', 'MaterialPreset'
    ];

    coreNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Material/Core',
        icon: 'palette',
        color: '#FF9800',
        tags: ['material', 'core', 'batch01'],
        nodeClass: null // 将在运行时从引擎获取
      });
    });

    // 材质编辑节点 (9个)
    const editorNodes = [
      'MaterialEditor', 'MaterialPreview', 'MaterialLibrary', 'MaterialImport',
      'MaterialExport', 'MaterialValidation', 'MaterialVersioning', 'MaterialSharing',
      'MaterialAnalytics'
    ];

    editorNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Material/Editor',
        icon: 'edit',
        color: '#FFC107',
        tags: ['material', 'editor', 'batch01'],
        nodeClass: null
      });
    });

    // 高级材质节点 (4个)
    const advancedNodes = [
      'MaterialNodeEditor', 'MaterialShaderEditor', 'MaterialTextureEditor', 'MaterialParameterEditor'
    ];

    advancedNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Material/Advanced',
        icon: 'experiment',
        color: '#FFEB3B',
        tags: ['material', 'advanced', 'batch01'],
        nodeClass: null
      });
    });
  }

  /**
   * 集成后处理效果节点 (15个)
   */
  private integratePostProcessingNodes(): void {
    const postProcessNodes = [
      'BloomEffect', 'BlurEffect', 'ColorGrading', 'ToneMapping', 'SSAO', 'SSR',
      'MotionBlur', 'DepthOfField', 'FilmGrain', 'Vignette', 'ChromaticAberration',
      'LensDistortion', 'AntiAliasing', 'HDRProcessing', 'CustomPostProcess'
    ];

    postProcessNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Rendering/PostProcess',
        icon: 'filter',
        color: '#E91E63',
        tags: ['postprocess', 'effect', 'batch01'],
        nodeClass: null
      });
    });
  }

  /**
   * 集成着色器节点 (15个)
   */
  private integrateShaderNodes(): void {
    // 核心着色器节点 (5个)
    const coreShaderNodes = [
      'VertexShader', 'FragmentShader', 'ComputeShader', 'ShaderCompiler', 'ShaderOptimization'
    ];

    coreShaderNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Shader/Core',
        icon: 'code',
        color: '#9C27B0',
        tags: ['shader', 'core', 'batch01'],
        nodeClass: null
      });
    });

    // 高级着色器节点 (4个)
    const advancedShaderNodes = [
      'ShaderVariant', 'ShaderParameter', 'ShaderInclude', 'ShaderMacro'
    ];

    advancedShaderNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Shader/Advanced',
        icon: 'function',
        color: '#9C27B0',
        tags: ['shader', 'advanced', 'batch01'],
        nodeClass: null
      });
    });

    // 着色器工具节点 (6个)
    const utilityShaderNodes = [
      'ShaderDebug', 'ShaderPerformanceAnalysis', 'ShaderValidation',
      'ShaderCache', 'ShaderHotReload', 'ShaderExport'
    ];

    utilityShaderNodes.forEach(nodeType => {
      const isDebug = ['ShaderDebug', 'ShaderPerformanceAnalysis', 'ShaderValidation'].includes(nodeType);
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: isDebug ? 'Shader/Debug' : 'Shader/Utility',
        icon: isDebug ? 'bug' : 'tool',
        color: isDebug ? '#F44336' : '#009688',
        tags: ['shader', isDebug ? 'debug' : 'utility', 'batch01'],
        nodeClass: null
      });
    });
  }

  /**
   * 集成渲染优化节点 (15个)
   */
  private integrateRenderingOptimizationNodes(): void {
    // 核心优化节点 (5个)
    const coreOptNodes = [
      'LODSystem', 'FrustumCulling', 'OcclusionCulling', 'BatchRendering', 'InstancedRendering'
    ];

    coreOptNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Rendering/Optimization',
        icon: 'speed',
        color: '#4CAF50',
        tags: ['optimization', 'performance', 'batch01'],
        nodeClass: null
      });
    });

    // 性能优化节点 (5个)
    const perfOptNodes = [
      'DrawCallOptimization', 'TextureAtlas', 'MeshCombining', 'RenderQueue', 'PerformanceProfiler'
    ];

    perfOptNodes.forEach(nodeType => {
      const isAnalysis = nodeType === 'PerformanceProfiler';
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: isAnalysis ? 'Rendering/Analysis' : 'Rendering/Optimization',
        icon: isAnalysis ? 'analytics' : 'speed',
        color: '#4CAF50',
        tags: ['optimization', isAnalysis ? 'analysis' : 'performance', 'batch01'],
        nodeClass: null
      });
    });

    // 监控和管线节点 (5个)
    const pipelineNodes = [
      'RenderStatistics', 'GPUMemoryMonitor', 'RenderPipeline', 'CustomRenderPass', 'RenderTarget'
    ];

    pipelineNodes.forEach(nodeType => {
      const isAnalysis = ['RenderStatistics', 'GPUMemoryMonitor'].includes(nodeType);
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: isAnalysis ? 'Rendering/Analysis' : 'Rendering/Pipeline',
        icon: isAnalysis ? 'analytics' : 'linear_scale',
        color: '#4CAF50',
        tags: ['rendering', isAnalysis ? 'analysis' : 'pipeline', 'batch01'],
        nodeClass: null
      });
    });
  }

  /**
   * 注册节点
   */
  private registerNode(config: Batch01NodeConfig): void {
    this.registeredNodes.set(config.type, config);
    
    // 添加到分类
    const categoryNodes = this.categoryNodes.get(config.category) || [];
    categoryNodes.push(config);
    this.categoryNodes.set(config.category, categoryNodes);
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    // 将节点添加到编辑器的节点面板
    for (const [nodeType, nodeConfig] of this.registeredNodes.entries()) {
      this.nodeEditor.addNodeToPalette(nodeType, nodeConfig);
    }

    console.log('批次0.1节点面板设置完成: 69个节点已添加到编辑器');
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    // 为编辑器添加新的节点分类
    for (const [category, categoryInfo] of Object.entries(BATCH01_CATEGORY_MAP)) {
      this.nodeEditor.addNodeCategory(category, {
        displayName: categoryInfo.displayName,
        icon: categoryInfo.icon,
        color: categoryInfo.color,
        description: categoryInfo.description,
        nodes: this.categoryNodes.get(category) || []
      });
    }

    console.log('批次0.1节点分类设置完成: 11个分类已添加到编辑器');
  }

  /**
   * 获取节点显示名称
   */
  private getNodeDisplayName(nodeType: string): string {
    // 简单的驼峰命名转换为中文显示名称
    const nameMap: Record<string, string> = {
      'MaterialSystem': '材质系统',
      'CreateMaterial': '创建材质',
      'SetMaterialProperty': '设置材质属性',
      'GetMaterialProperty': '获取材质属性',
      'MaterialBlend': '材质混合',
      'MaterialAnimation': '材质动画',
      'MaterialOptimization': '材质优化',
      'PBRMaterial': 'PBR材质',
      'StandardMaterial': '标准材质',
      'CustomMaterial': '自定义材质',
      'MaterialPreset': '材质预设',
      'MaterialEditor': '材质编辑器',
      'MaterialPreview': '材质预览',
      'MaterialLibrary': '材质库',
      'BloomEffect': '泛光效果',
      'BlurEffect': '模糊效果',
      'ColorGrading': '颜色分级',
      'VertexShader': '顶点着色器',
      'FragmentShader': '片段着色器',
      'ComputeShader': '计算着色器',
      'LODSystem': 'LOD系统',
      'FrustumCulling': '视锥体剔除',
      'OcclusionCulling': '遮挡剔除'
      // 可以继续添加更多映射
    };

    return nameMap[nodeType] || nodeType;
  }

  /**
   * 获取节点描述
   */
  private getNodeDescription(nodeType: string): string {
    return `批次0.1渲染系统节点 - ${this.getNodeDisplayName(nodeType)}`;
  }

  /**
   * 获取已注册的节点
   */
  public getRegisteredNodes(): Map<string, Batch01NodeConfig> {
    return this.registeredNodes;
  }

  /**
   * 获取节点分类
   */
  public getNodeCategories(): Map<string, Batch01NodeConfig[]> {
    return this.categoryNodes;
  }

  /**
   * 检查节点是否已注册
   */
  public isNodeRegistered(nodeType: string): boolean {
    return this.registeredNodes.has(nodeType);
  }

  /**
   * 获取节点配置
   */
  public getNodeConfig(nodeType: string): Batch01NodeConfig | undefined {
    return this.registeredNodes.get(nodeType);
  }
}

/**
 * 导出集成函数
 */
export function integrateBatch01Nodes(nodeEditor: NodeEditor): Batch01NodesIntegration {
  const integration = new Batch01NodesIntegration(nodeEditor);
  integration.integrateAllNodes();
  return integration;
}
