{"batch01": {"title": "批次0.1渲染系统节点", "description": "69个渲染系统节点，包括材质管理、后处理效果、着色器、渲染优化等功能", "categories": {"Material/Core": {"name": "材质核心", "description": "核心材质管理功能", "nodes": {"MaterialSystem": "材质系统", "CreateMaterial": "创建材质", "SetMaterialProperty": "设置材质属性", "GetMaterialProperty": "获取材质属性", "MaterialBlend": "材质混合", "MaterialAnimation": "材质动画", "MaterialOptimization": "材质优化", "PBRMaterial": "PBR材质", "StandardMaterial": "标准材质", "CustomMaterial": "自定义材质", "MaterialPreset": "材质预设"}}, "Material/Editor": {"name": "材质编辑", "description": "材质编辑和管理工具", "nodes": {"MaterialEditor": "材质编辑器", "MaterialPreview": "材质预览", "MaterialLibrary": "材质库", "MaterialImport": "材质导入", "MaterialExport": "材质导出", "MaterialValidation": "材质验证", "MaterialVersioning": "材质版本控制", "MaterialSharing": "材质共享", "MaterialAnalytics": "材质分析"}}, "Material/Advanced": {"name": "高级材质", "description": "高级材质编辑功能", "nodes": {"MaterialNodeEditor": "材质节点编辑器", "MaterialShaderEditor": "材质着色器编辑器", "MaterialTextureEditor": "材质纹理编辑器", "MaterialParameterEditor": "材质参数编辑器"}}, "Rendering/PostProcess": {"name": "后处理效果", "description": "后处理效果和滤镜", "nodes": {"BloomEffect": "泛光效果", "BlurEffect": "模糊效果", "ColorGrading": "颜色分级", "ToneMapping": "色调映射", "SSAO": "屏幕空间环境光遮蔽", "SSR": "屏幕空间反射", "MotionBlur": "运动模糊", "DepthOfField": "景深", "FilmGrain": "胶片颗粒", "Vignette": "暗角效果", "ChromaticAberration": "色差", "LensDistortion": "镜头畸变", "AntiAliasing": "抗锯齿", "HDRProcessing": "HDR处理", "CustomPostProcess": "自定义后处理"}}, "Shader/Core": {"name": "核心着色器", "description": "核心着色器功能", "nodes": {"VertexShader": "顶点着色器", "FragmentShader": "片段着色器", "ComputeShader": "计算着色器", "ShaderCompiler": "着色器编译器", "ShaderOptimization": "着色器优化"}}, "Shader/Advanced": {"name": "高级着色器", "description": "高级着色器功能", "nodes": {"ShaderVariant": "着色器变体", "ShaderParameter": "着色器参数", "ShaderInclude": "着色器包含", "ShaderMacro": "着色器宏"}}, "Shader/Debug": {"name": "着色器调试", "description": "着色器调试工具", "nodes": {"ShaderDebug": "着色器调试", "ShaderPerformanceAnalysis": "着色器性能分析", "ShaderValidation": "着色器验证"}}, "Shader/Utility": {"name": "着色器工具", "description": "着色器实用工具", "nodes": {"ShaderCache": "着色器缓存", "ShaderHotReload": "着色器热重载", "ShaderExport": "着色器导出"}}, "Rendering/Optimization": {"name": "渲染优化", "description": "渲染性能优化", "nodes": {"LODSystem": "LOD系统", "FrustumCulling": "视锥体剔除", "OcclusionCulling": "遮挡剔除", "BatchRendering": "批处理渲染", "InstancedRendering": "实例化渲染", "DrawCallOptimization": "绘制调用优化", "TextureAtlas": "纹理图集", "MeshCombining": "网格合并", "RenderQueue": "渲染队列", "PerformanceProfiler": "性能分析器"}}, "Rendering/Analysis": {"name": "渲染分析", "description": "渲染性能分析", "nodes": {"RenderStatistics": "渲染统计", "GPUMemoryMonitor": "GPU内存监控", "RenderingProfiler": "渲染分析器"}}, "Rendering/Pipeline": {"name": "渲染管线", "description": "渲染管线管理", "nodes": {"RenderPipeline": "渲染管线", "CustomRenderPass": "自定义渲染通道", "RenderTarget": "渲染目标"}}}, "tags": {"batch01": "批次0.1", "rendering": "渲染", "material": "材质", "shader": "着色器", "postprocess": "后处理", "optimization": "优化", "performance": "性能", "graphics": "图形", "glsl": "GLSL", "effect": "效果", "analysis": "分析", "pipeline": "管线", "core": "核心", "advanced": "高级", "editor": "编辑器", "debug": "调试", "utility": "工具"}, "tooltips": {"MaterialSystem": "管理整个材质系统，包括材质创建、更新和删除", "CreateMaterial": "创建新的材质实例，支持多种材质类型", "BloomEffect": "添加泛光效果，让明亮区域产生光晕", "VertexShader": "编译和管理顶点着色器程序", "LODSystem": "根据距离自动切换模型细节级别", "RenderStatistics": "收集和显示渲染性能统计信息"}, "errors": {"nodeNotFound": "未找到指定的节点", "categoryNotFound": "未找到指定的分类", "registrationFailed": "节点注册失败", "integrationFailed": "节点集成失败", "invalidConfiguration": "无效的节点配置"}, "success": {"nodeRegistered": "节点注册成功", "categoryCreated": "分类创建成功", "integrationComplete": "集成完成", "nodesLoaded": "节点加载完成"}, "statistics": {"totalNodes": "总节点数", "materialNodes": "材质节点", "shaderNodes": "着色器节点", "postProcessNodes": "后处理节点", "optimizationNodes": "优化节点", "categories": "分类数量", "registeredNodes": "已注册节点", "activeNodes": "活跃节点"}, "actions": {"addToFavorites": "添加到收藏", "removeFromFavorites": "从收藏中移除", "viewDocumentation": "查看文档", "openExample": "打开示例", "copyNode": "复制节点", "deleteNode": "删除节点", "editProperties": "编辑属性", "connectNodes": "连接节点", "disconnectNodes": "断开连接"}, "search": {"placeholder": "搜索渲染系统节点...", "noResults": "没有找到匹配的节点", "filterByCategory": "按分类筛选", "filterByTag": "按标签筛选", "clearFilters": "清除筛选", "showAll": "显示全部", "recentlyUsed": "最近使用", "mostPopular": "最受欢迎"}, "help": {"gettingStarted": "开始使用批次0.1节点", "materialWorkflow": "材质工作流程", "shaderDevelopment": "着色器开发", "performanceOptimization": "性能优化指南", "troubleshooting": "故障排除", "bestPractices": "最佳实践", "examples": "示例项目", "apiReference": "API参考"}}}